#ifndef LINK_PROTOCOL_H
#define LINK_PROTOCOL_H
#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>
#include <string>

#ifdef __cplusplus
extern "C" {
#endif

// Bluetooth scanning function declarations
void ble_app_scan(void);
std::string ble_scan_start_and_wait_json();
void ble_app_on_sync(void);
void host_task(void *param);

#ifdef __cplusplus
}
#endif

#endif // LINK_PROTOCOL_H
